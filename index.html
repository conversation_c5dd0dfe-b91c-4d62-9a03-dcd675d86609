<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/app-review-today.svg" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#2DD4BF" />

    <!-- Preconnect to critical origins for performance -->
    <link rel="preconnect" href="https://js.stripe.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- DNS prefetch for additional performance -->
    <link rel="dns-prefetch" href="https://api.stripe.com">
    <link rel="dns-prefetch" href="https://checkout.stripe.com">

    <title>AppReview.Today - AI-Powered User Review Analysis</title>
    <meta name="description" content="Generate comprehensive reports from user reviews across App Store, Google Play, Reddit, and more. Get actionable insights in minutes." />

  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>